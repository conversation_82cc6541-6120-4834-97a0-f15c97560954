#!/usr/bin/env python3
"""
Simple script to test and restart the server
"""
import os
import sys
import subprocess

def restart_server():
    """Restart the FastAPI server"""
    try:
        print("🔄 Restarting FastAPI server...")
        
        # Change to backend directory
        backend_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(backend_dir)
        
        # Check if config exists
        if os.path.exists('config.env'):
            print("✅ Config file found")
        else:
            print("⚠️ Config file not found")
        
        # Start server
        print("🚀 Starting server on port 8000...")
        subprocess.run([
            sys.executable, "main.py"
        ], check=True)
        
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Failed to start server: {e}")

if __name__ == "__main__":
    restart_server()

