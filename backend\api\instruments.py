"""
各仪器的API路由定义
每个仪器都有自己独立的路由模块
"""
from fastapi import APIRouter, HTTPException, Query
from typing import List, Optional
from datetime import datetime, timedelta

from models.instruments import (
    GasAnalyzerData,
    FlowMeterData, 
    ShaftPowerData,
    RHMeterData,
    ESP32Data,
    DataQueryParams,
    APIResponse
)
from database.connection import db_manager

# Gas Analyzer 路由
gas_analyzer_router = APIRouter()

@gas_analyzer_router.get("/latest", response_model=List[GasAnalyzerData])
async def get_latest_gas_analyzer_data(
    device_number: Optional[int] = Query(None, description="设备编号"),
    limit: int = Query(10, ge=1, le=100, description="返回记录数")
):
    """获取气体分析仪最新数据"""
    try:
        # 构建查询条件
        where_clause = ""
        if device_number is not None:
            where_clause = f"WHERE device_number = {device_number}"
        
        # 获取表列表
        instrument_tables = await db_manager.get_instrument_tables()
        tables = instrument_tables.get("gas_analyzer", [])
        
        if not tables:
            return []
        
        all_data = []
        for table in tables:
            try:
                query = f"""
                SELECT * FROM {table} 
                {where_clause}
                ORDER BY timestamp DESC 
                LIMIT {limit}
                """
                data = await db_manager.execute_query(query)
                all_data.extend(data)
            except Exception as e:
                print(f"读取表 {table} 失败: {e}")
                continue
        
        # 按时间排序
        all_data.sort(key=lambda x: x.get("timestamp", datetime.min), reverse=True)
        
        return all_data[:limit]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取气体分析仪数据失败: {str(e)}")

@gas_analyzer_router.get("/history")
async def get_gas_analyzer_history(
    device_number: Optional[int] = Query(None),
    start_time: Optional[datetime] = Query(None),
    end_time: Optional[datetime] = Query(None),
    limit: int = Query(1000, ge=1, le=10000)
):
    """获取气体分析仪历史数据"""
    try:
        # 构建查询条件
        conditions = []
        if device_number is not None:
            conditions.append(f"device_number = {device_number}")
        if start_time:
            conditions.append(f"timestamp >= '{start_time}'")
        if end_time:
            conditions.append(f"timestamp <= '{end_time}'")
        
        where_clause = ""
        if conditions:
            where_clause = "WHERE " + " AND ".join(conditions)
        
        # 获取数据
        instrument_tables = await db_manager.get_instrument_tables()
        tables = instrument_tables.get("gas_analyzer", [])
        
        all_data = []
        for table in tables:
            try:
                query = f"""
                SELECT * FROM {table} 
                {where_clause}
                ORDER BY timestamp DESC 
                LIMIT {limit}
                """
                data = await db_manager.execute_query(query)
                all_data.extend(data)
            except Exception as e:
                continue
        
        return APIResponse(
            data=all_data[:limit],
            total=len(all_data)
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取历史数据失败: {str(e)}")

# Flow Meter 路由
flow_meter_router = APIRouter()

@flow_meter_router.get("/latest", response_model=List[FlowMeterData])
async def get_latest_flow_meter_data(
    device_number: Optional[int] = Query(None),
    limit: int = Query(10, ge=1, le=100)
):
    """获取质量流量计最新数据"""
    try:
        where_clause = ""
        if device_number is not None:
            where_clause = f"WHERE device_number = {device_number}"
        
        instrument_tables = await db_manager.get_instrument_tables()
        tables = instrument_tables.get("flow_meter", [])
        
        all_data = []
        for table in tables:
            try:
                query = f"""
                SELECT * FROM {table} 
                {where_clause}
                ORDER BY timestamp DESC 
                LIMIT {limit}
                """
                data = await db_manager.execute_query(query)
                all_data.extend(data)
            except Exception:
                continue
        
        all_data.sort(key=lambda x: x.get("timestamp", datetime.min), reverse=True)
        return all_data[:limit]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取流量计数据失败: {str(e)}")

@flow_meter_router.get("/history")
async def get_flow_meter_history(
    device_number: Optional[int] = Query(None),
    start_time: Optional[datetime] = Query(None),
    end_time: Optional[datetime] = Query(None),
    limit: int = Query(1000, ge=1, le=10000)
):
    """获取质量流量计历史数据"""
    try:
        conditions = []
        if device_number is not None:
            conditions.append(f"device_number = {device_number}")
        if start_time:
            conditions.append(f"timestamp >= '{start_time}'")
        if end_time:
            conditions.append(f"timestamp <= '{end_time}'")
        
        where_clause = ""
        if conditions:
            where_clause = "WHERE " + " AND ".join(conditions)
        
        instrument_tables = await db_manager.get_instrument_tables()
        tables = instrument_tables.get("flow_meter", [])
        
        all_data = []
        for table in tables:
            try:
                query = f"""
                SELECT * FROM {table} 
                {where_clause}
                ORDER BY timestamp DESC 
                LIMIT {limit}
                """
                data = await db_manager.execute_query(query)
                all_data.extend(data)
            except Exception:
                continue
        
        return APIResponse(
            data=all_data[:limit],
            total=len(all_data)
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取历史数据失败: {str(e)}")

# Shaft Power 路由
shaft_power_router = APIRouter()

@shaft_power_router.get("/latest", response_model=List[ShaftPowerData])
async def get_latest_shaft_power_data(
    device_number: Optional[int] = Query(None),
    limit: int = Query(10, ge=1, le=100)
):
    """获取轴功率计最新数据"""
    try:
        where_clause = ""
        if device_number is not None:
            where_clause = f"WHERE device_number = {device_number}"
        
        instrument_tables = await db_manager.get_instrument_tables()
        tables = instrument_tables.get("shaft_power", [])
        
        all_data = []
        for table in tables:
            try:
                query = f"""
                SELECT * FROM {table} 
                {where_clause}
                ORDER BY timestamp DESC 
                LIMIT {limit}
                """
                data = await db_manager.execute_query(query)
                all_data.extend(data)
            except Exception:
                continue
        
        all_data.sort(key=lambda x: x.get("timestamp", datetime.min), reverse=True)
        return all_data[:limit]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取轴功率数据失败: {str(e)}")

@shaft_power_router.get("/history")
async def get_shaft_power_history(
    device_number: Optional[int] = Query(None),
    start_time: Optional[datetime] = Query(None),
    end_time: Optional[datetime] = Query(None),
    limit: int = Query(1000, ge=1, le=10000)
):
    """获取轴功率计历史数据"""
    try:
        conditions = []
        if device_number is not None:
            conditions.append(f"device_number = {device_number}")
        if start_time:
            conditions.append(f"timestamp >= '{start_time}'")
        if end_time:
            conditions.append(f"timestamp <= '{end_time}'")
        
        where_clause = ""
        if conditions:
            where_clause = "WHERE " + " AND ".join(conditions)
        
        instrument_tables = await db_manager.get_instrument_tables()
        tables = instrument_tables.get("shaft_power", [])
        
        all_data = []
        for table in tables:
            try:
                query = f"""
                SELECT * FROM {table} 
                {where_clause}
                ORDER BY timestamp DESC 
                LIMIT {limit}
                """
                data = await db_manager.execute_query(query)
                all_data.extend(data)
            except Exception:
                continue
        
        return APIResponse(
            data=all_data[:limit],
            total=len(all_data)
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取历史数据失败: {str(e)}")

# RH Meter 路由
rh_meter_router = APIRouter()

@rh_meter_router.get("/latest", response_model=List[RHMeterData])
async def get_latest_rh_meter_data(
    device_number: Optional[int] = Query(None),
    limit: int = Query(10, ge=1, le=100)
):
    """获取温湿度计最新数据"""
    try:
        where_clause = ""
        if device_number is not None:
            where_clause = f"WHERE device_number = {device_number}"
        
        instrument_tables = await db_manager.get_instrument_tables()
        tables = instrument_tables.get("rh_meter", [])
        
        all_data = []
        for table in tables:
            try:
                query = f"""
                SELECT * FROM {table} 
                {where_clause}
                ORDER BY timestamp DESC 
                LIMIT {limit}
                """
                data = await db_manager.execute_query(query)
                all_data.extend(data)
            except Exception:
                continue
        
        all_data.sort(key=lambda x: x.get("timestamp", datetime.min), reverse=True)
        return all_data[:limit]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取温湿度数据失败: {str(e)}")

@rh_meter_router.get("/history")
async def get_rh_meter_history(
    device_number: Optional[int] = Query(None),
    start_time: Optional[datetime] = Query(None),
    end_time: Optional[datetime] = Query(None),
    limit: int = Query(1000, ge=1, le=10000)
):
    """获取温湿度计历史数据"""
    try:
        conditions = []
        if device_number is not None:
            conditions.append(f"device_number = {device_number}")
        if start_time:
            conditions.append(f"timestamp >= '{start_time}'")
        if end_time:
            conditions.append(f"timestamp <= '{end_time}'")
        
        where_clause = ""
        if conditions:
            where_clause = "WHERE " + " AND ".join(conditions)
        
        instrument_tables = await db_manager.get_instrument_tables()
        tables = instrument_tables.get("rh_meter", [])
        
        all_data = []
        for table in tables:
            try:
                query = f"""
                SELECT * FROM {table} 
                {where_clause}
                ORDER BY timestamp DESC 
                LIMIT {limit}
                """
                data = await db_manager.execute_query(query)
                all_data.extend(data)
            except Exception:
                continue
        
        return APIResponse(
            data=all_data[:limit],
            total=len(all_data)
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取历史数据失败: {str(e)}")

# ESP32 路由
esp32_router = APIRouter()

@esp32_router.get("/latest", response_model=List[ESP32Data])
async def get_latest_esp32_data(
    device_number: Optional[int] = Query(None),
    limit: int = Query(10, ge=1, le=100)
):
    """获取ESP32最新数据"""
    try:
        where_clause = ""
        if device_number is not None:
            where_clause = f"WHERE device_number = {device_number}"
        
        instrument_tables = await db_manager.get_instrument_tables()
        tables = instrument_tables.get("esp32", [])
        
        all_data = []
        for table in tables:
            try:
                query = f"""
                SELECT * FROM {table} 
                {where_clause}
                ORDER BY timestamp DESC 
                LIMIT {limit}
                """
                data = await db_manager.execute_query(query)
                all_data.extend(data)
            except Exception:
                continue
        
        all_data.sort(key=lambda x: x.get("timestamp", datetime.min), reverse=True)
        return all_data[:limit]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取ESP32数据失败: {str(e)}")

@esp32_router.get("/history")
async def get_esp32_history(
    device_number: Optional[int] = Query(None),
    start_time: Optional[datetime] = Query(None),
    end_time: Optional[datetime] = Query(None),
    limit: int = Query(1000, ge=1, le=10000)
):
    """获取ESP32历史数据"""
    try:
        conditions = []
        if device_number is not None:
            conditions.append(f"device_number = {device_number}")
        if start_time:
            conditions.append(f"timestamp >= '{start_time}'")
        if end_time:
            conditions.append(f"timestamp <= '{end_time}'")
        
        where_clause = ""
        if conditions:
            where_clause = "WHERE " + " AND ".join(conditions)
        
        instrument_tables = await db_manager.get_instrument_tables()
        tables = instrument_tables.get("esp32", [])
        
        all_data = []
        for table in tables:
            try:
                query = f"""
                SELECT * FROM {table} 
                {where_clause}
                ORDER BY timestamp DESC 
                LIMIT {limit}
                """
                data = await db_manager.execute_query(query)
                all_data.extend(data)
            except Exception:
                continue
        
        return APIResponse(
            data=all_data[:limit],
            total=len(all_data)
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取历史数据失败: {str(e)}")
