#!/usr/bin/env python3
"""
独立的GPS数据提取器
每5秒从MySQL提取最新GPS数据，存储到简单的缓存文件中
"""
import asyncio
import json
import os
import sys
import time
from datetime import datetime
from typing import Dict, Optional
import logging

# Add the backend directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database.connection import db_manager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class GPSDataExtractor:
    """GPS数据提取器"""
    
    def __init__(self, cache_file: str = "gps_cache.json"):
        self.cache_file = cache_file
        self.running = False
        
    async def get_latest_gps_data(self) -> Optional[Dict]:
        """获取最新的GPS数据"""
        try:
            # 获取所有GPS表
            instrument_tables = await db_manager.get_instrument_tables()
            gps_tables = instrument_tables.get('gps', [])
            
            if not gps_tables:
                logger.warning("No GPS tables found")
                return None
            
            # 过滤并排序GPS表（优先选择带日期的表）
            date_gps_tables = [t for t in gps_tables if t.startswith('gps_data_') and len(t) > 20]
            if date_gps_tables:
                # 按名称排序，最新的在前
                date_gps_tables.sort(reverse=True)
                latest_table = date_gps_tables[0]
            else:
                # 如果没有日期格式的表，使用第一个GPS表
                latest_table = gps_tables[0]
            
            logger.info(f"使用GPS表: {latest_table}")
            
            # 从最新表获取最新记录
            query = f"""
            SELECT id, datetime_rp, lat, lon, velD, velE, velN, mode, altHAE, altMSL
            FROM {latest_table} 
            ORDER BY id DESC 
            LIMIT 1
            """
            
            data = await db_manager.execute_query(query)
            
            if data and len(data) > 0:
                record = data[0]
                
                # 检查GPS数据有效性
                lat = record.get('lat')
                lon = record.get('lon')
                
                if lat is None or lon is None:
                    logger.warning("GPS coordinates are None")
                    return None
                
                # 转换为float并检查有效性
                try:
                    lat = float(lat)
                    lon = float(lon)
                    
                    if lat == 0 and lon == 0:
                        logger.warning("GPS coordinates are zero")
                        return None
                    
                    # 检查坐标是否在合理范围内 (世界范围)
                    if not (-90 <= lat <= 90) or not (-180 <= lon <= 180):
                        logger.warning(f"GPS coordinates out of range: lat={lat}, lon={lon}")
                        return None
                    
                except (ValueError, TypeError):
                    logger.warning(f"Invalid GPS coordinates: lat={lat}, lon={lon}")
                    return None
                
                # 计算速度
                velD = float(record.get('velD', 0))
                velE = float(record.get('velE', 0))
                velN = float(record.get('velN', 0))
                
                # 计算总速度: sqrt(VelD² + VelE² + VelN²) * 3.6 (m/s to km/h)
                speed_ms = (velD**2 + velE**2 + velN**2)**0.5
                speed_kmh = speed_ms * 3.6
                
                # 构建返回数据
                gps_data = {
                    'id': record.get('id'),
                    'table': latest_table,
                    'timestamp': record.get('datetime_rp').isoformat() if record.get('datetime_rp') else None,
                    'lat': lat,
                    'lon': lon,
                    'altitude': record.get('altHAE'),
                    'altitude_msl': record.get('altMSL'),
                    'mode': record.get('mode'),
                    'speed': {
                        'kmh': round(speed_kmh, 2),
                        'ms': round(speed_ms, 2),
                        'components': {
                            'velD': velD,
                            'velE': velE,
                            'velN': velN
                        }
                    },
                    'updated_at': datetime.now().isoformat()
                }
                
                logger.info(f"GPS数据提取成功: lat={lat:.6f}, lon={lon:.6f}, speed={speed_kmh:.1f}km/h")
                return gps_data
            
            else:
                logger.warning("No GPS records found in latest table")
                return None
                
        except Exception as e:
            logger.error(f"获取GPS数据失败: {e}")
            return None
    
    def save_to_cache(self, data: Dict):
        """保存数据到缓存文件"""
        try:
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            logger.debug("GPS数据已保存到缓存")
        except Exception as e:
            logger.error(f"保存缓存失败: {e}")
    
    def load_from_cache(self) -> Optional[Dict]:
        """从缓存文件加载数据"""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"加载缓存失败: {e}")
        return None
    
    async def start_extraction(self, interval: int = 5):
        """开始数据提取循环"""
        logger.info(f"开始GPS数据提取，间隔: {interval}秒")
        self.running = True
        
        # 初始化数据库连接
        await db_manager.initialize()
        
        try:
            while self.running:
                # 提取最新GPS数据
                gps_data = await self.get_latest_gps_data()
                
                if gps_data:
                    # 保存到缓存
                    self.save_to_cache(gps_data)
                    logger.info(f"GPS数据更新: {gps_data['lat']:.6f}, {gps_data['lon']:.6f}")
                else:
                    logger.warning("未获取到有效GPS数据")
                
                # 等待下次提取
                await asyncio.sleep(interval)
                
        except KeyboardInterrupt:
            logger.info("用户停止GPS数据提取")
        except Exception as e:
            logger.error(f"GPS数据提取循环出错: {e}")
        finally:
            self.running = False
            await db_manager.close()
            logger.info("GPS数据提取器已停止")
    
    def stop_extraction(self):
        """停止数据提取"""
        self.running = False

# 测试函数
async def test_gps_extractor():
    """测试GPS数据提取器"""
    extractor = GPSDataExtractor("test_gps_cache.json")
    
    # 初始化数据库
    await db_manager.initialize()
    
    # 测试单次提取
    gps_data = await extractor.get_latest_gps_data()
    
    if gps_data:
        print("✅ GPS数据提取测试成功:")
        print(f"  位置: {gps_data['lat']:.6f}, {gps_data['lon']:.6f}")
        print(f"  速度: {gps_data['speed']['kmh']} km/h")
        print(f"  时间: {gps_data['timestamp']}")
        print(f"  表名: {gps_data['table']}")
        
        # 保存到缓存
        extractor.save_to_cache(gps_data)
        print("✅ 数据已保存到缓存")
        
    else:
        print("❌ GPS数据提取失败")
    
    await db_manager.close()

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        # 测试模式
        asyncio.run(test_gps_extractor())
    else:
        # 正常运行模式
        extractor = GPSDataExtractor()
        try:
            asyncio.run(extractor.start_extraction())
        except KeyboardInterrupt:
            print("\n停止GPS数据提取器")




