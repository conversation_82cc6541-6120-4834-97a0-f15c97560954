#!/usr/bin/env python3
"""
Test operation profile endpoint
"""
import asyncio
import sys
import os

# Add the backend directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database.connection import db_manager
from api.routes import get_operation_profile

async def test_operation_profile():
    """Test operation profile endpoint"""
    try:
        print("Testing operation profile endpoint...")
        
        # Initialize database manager
        await db_manager.initialize()
        
        # Test operation profile API
        profile_data = await get_operation_profile()
        
        print(f"✅ Operation profile data retrieved successfully")
        print(f"System status: {profile_data.system_status}")
        print(f"Number of alerts: {len(profile_data.alerts)}")
        
        # Check instruments
        print(f"\n📊 Instrument data:")
        for instrument, data in profile_data.instruments.items():
            print(f"  {instrument}: {len(data)} records")
            if data:
                latest = data[0]
                timestamp = latest.get('timestamp', 'N/A')
                print(f"    Latest: {timestamp}")
        
        # Check GPS specifically
        gps_data = profile_data.instruments.get('gps', [])
        if gps_data:
            gps_latest = gps_data[0]
            print(f"\n🛰️ GPS data details:")
            print(f"  Lat: {gps_latest.get('lat')}")
            print(f"  Lon: {gps_latest.get('lon')}")
            print(f"  Timestamp: {gps_latest.get('timestamp')}")
        
        # Close connection
        await db_manager.close()
        print("\n✅ Operation profile test completed")
        
    except Exception as e:
        print(f"❌ Operation profile test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_operation_profile())

