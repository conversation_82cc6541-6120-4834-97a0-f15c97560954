#!/usr/bin/env python3
"""
Test GPS API endpoint
"""
import asyncio
import sys
import os

# Add the backend directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database.connection import db_manager
from api.routes import get_gps_latest

async def test_gps_api():
    """Test GPS API endpoint"""
    try:
        print("Testing GPS API endpoint...")
        
        # Initialize database manager
        await db_manager.initialize()
        
        # Test GPS API
        gps_data = await get_gps_latest()
        
        print(f"📍 GPS API returned {len(gps_data)} records")
        
        if gps_data:
            latest = gps_data[0]
            print(f"Latest GPS data:")
            print(f"  ID: {latest.get('id')}")
            print(f"  Lat: {latest.get('lat')}")
            print(f"  Lon: {latest.get('lon')}")
            print(f"  VelD: {latest.get('velD')}")
            print(f"  VelE: {latest.get('velE')}")
            print(f"  VelN: {latest.get('velN')}")
            print(f"  Timestamp: {latest.get('datetime_rp')}")
            
            # Calculate speed
            velD = float(latest.get('velD', 0))
            velE = float(latest.get('velE', 0))
            velN = float(latest.get('velN', 0))
            
            speed_ms = (velD**2 + velE**2 + velN**2)**0.5
            speed_kmh = speed_ms * 3.6
            
            print(f"  Calculated Speed: {speed_kmh:.2f} km/h")
        else:
            print("⚠️ No GPS data returned from API")
        
        # Close connection
        await db_manager.close()
        print("\n✅ GPS API test completed")
        
    except Exception as e:
        print(f"❌ GPS API test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_gps_api())

