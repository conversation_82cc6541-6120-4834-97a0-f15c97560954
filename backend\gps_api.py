#!/usr/bin/env python3
"""
简化的GPS API
读取GPS缓存文件，提供简单的GPS数据API
"""
import json
import os
from typing import Optional, Dict
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

app = FastAPI(title="GPS Data API", description="Simple GPS Data Service", version="1.0.0")

# CORS设置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

GPS_CACHE_FILE = "gps_cache.json"

def load_gps_cache() -> Optional[Dict]:
    """加载GPS缓存数据"""
    try:
        if os.path.exists(GPS_CACHE_FILE):
            with open(GPS_CACHE_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception as e:
        print(f"加载GPS缓存失败: {e}")
    return None

@app.get("/")
async def root():
    """根端点"""
    return {
        "message": "GPS Data API",
        "status": "running",
        "endpoints": ["/gps/latest", "/health"]
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    gps_data = load_gps_cache()
    cache_status = "available" if gps_data else "empty"
    
    return {
        "status": "healthy",
        "cache_status": cache_status,
        "cache_file": GPS_CACHE_FILE
    }

@app.get("/gps/latest")
async def get_latest_gps():
    """获取最新GPS数据"""
    try:
        gps_data = load_gps_cache()
        
        if not gps_data:
            raise HTTPException(status_code=404, detail="No GPS data available")
        
        return [gps_data]  # 返回数组格式以保持与前端兼容
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get GPS data: {str(e)}")

@app.get("/gps/info")
async def get_gps_info():
    """获取GPS信息"""
    try:
        gps_data = load_gps_cache()
        
        if not gps_data:
            return {
                "available": False,
                "message": "No GPS data available"
            }
        
        return {
            "available": True,
            "table": gps_data.get("table"),
            "last_update": gps_data.get("updated_at"),
            "coordinates": {
                "lat": gps_data.get("lat"),
                "lon": gps_data.get("lon")
            },
            "speed": gps_data.get("speed", {}).get("kmh", 0)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get GPS info: {str(e)}")

if __name__ == "__main__":
    print("🚀 启动GPS API服务器 (端口: 8001)")
    uvicorn.run("gps_api:app", host="0.0.0.0", port=8001, reload=True)




