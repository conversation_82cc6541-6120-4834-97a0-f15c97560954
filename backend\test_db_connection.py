#!/usr/bin/env python3
"""
Test database connection script
"""
import asyncio
import sys
import os

# Add the backend directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database.connection import db_manager

async def test_connection():
    """Test database connection"""
    try:
        print("Testing database connection...")
        
        # Initialize database manager
        await db_manager.initialize()
        print("✅ Database manager initialized successfully")
        
        # Test basic connection
        tables = await db_manager.get_table_list()
        print(f"✅ Connected to database. Found {len(tables)} tables:")
        
        for table in tables[:10]:  # Show first 10 tables
            print(f"  - {table}")
        
        if len(tables) > 10:
            print(f"  ... and {len(tables) - 10} more tables")
            
        # Test instrument tables
        instrument_tables = await db_manager.get_instrument_tables()
        print(f"\n📊 Instrument tables found:")
        for instrument, table_list in instrument_tables.items():
            print(f"  {instrument}: {len(table_list)} tables")
            for table in table_list[:3]:  # Show first 3 tables per instrument
                print(f"    - {table}")
            if len(table_list) > 3:
                print(f"    ... and {len(table_list) - 3} more")
        
        # Test GPS specific tables
        gps_tables = instrument_tables.get('gps', [])
        if gps_tables:
            print(f"\n🛰️ GPS tables found: {len(gps_tables)}")
            latest_gps_table = sorted(gps_tables, reverse=True)[0] if gps_tables else None
            
            if latest_gps_table:
                print(f"📍 Latest GPS table: {latest_gps_table}")
                
                # Test getting latest GPS data
                query = f"SELECT * FROM {latest_gps_table} ORDER BY id DESC LIMIT 1"
                gps_data = await db_manager.execute_query(query)
                
                if gps_data:
                    latest_record = gps_data[0]
                    print(f"📅 Latest GPS record:")
                    print(f"  ID: {latest_record.get('id', 'N/A')}")
                    print(f"  Lat: {latest_record.get('lat', 'N/A')}")
                    print(f"  Lon: {latest_record.get('lon', 'N/A')}")
                    print(f"  VelD: {latest_record.get('velD', 'N/A')}")
                    print(f"  VelE: {latest_record.get('velE', 'N/A')}")
                    print(f"  VelN: {latest_record.get('velN', 'N/A')}")
                else:
                    print("⚠️ No GPS data found in latest table")
        else:
            print("⚠️ No GPS tables found")
            
        # Close connection
        await db_manager.close()
        print("\n✅ Test completed successfully")
        
    except Exception as e:
        print(f"❌ Database connection test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_connection())

