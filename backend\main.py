"""
EEPM Data Metrics - FastAPI Backend
Main application entry point
"""
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import uvicorn
import os
from dotenv import load_dotenv

from database.connection import db_manager
from api.routes import router as api_router
from services.device_statistics import device_stats_service

# Load environment variables
load_dotenv()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifecycle management"""
    # On startup
    print("EEPM Data Metrics Backend Starting...")
    
    # Initialize database connection
    try:
        await db_manager.initialize()
        print("✅ Database connection initialized successfully")

        # Start device statistics service
        device_stats_service.start_background_updates()
        print("✅ Device statistics service started")

    except Exception as e:
        print(f"⚠️  Database connection failed: {e}")
        print("🔄 Continuing without database connection...")

    yield
    
    # On shutdown
    print("EEPM Data Metrics Backend Shutting down...")
    try:
        # Stop device statistics service
        device_stats_service.stop_background_updates()
        print("✅ Device statistics service stopped")

        await db_manager.close()
    except Exception as e:
        print(f"Database close error: {e}")

# Create FastAPI application
app = FastAPI(
    title="EEPM Data Metrics API",
    description="Marine Equipment Monitoring Data Management System API",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc",
    lifespan=lifespan
)

# CORS middleware configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=os.getenv("ALLOWED_ORIGINS", "http://localhost:3000").split(","),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(api_router, prefix="/api/v1")

@app.get("/")
async def root():
    """Root path health check"""
    return {
        "message": "EEPM Data Metrics API",
        "status": "healthy",
        "version": "1.0.0"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Return healthy status without database connection
        return {
            "status": "healthy",
            "database": "disconnected",
            "message": "Database connections disabled",
            "timestamp": "2024-01-01T00:00:00Z"
        }
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Health check failed: {str(e)}")

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=os.getenv("API_HOST", "0.0.0.0"),
        port=int(os.getenv("API_PORT", 3000)),
        reload=os.getenv("DEBUG", "False").lower() == "true"
    )
