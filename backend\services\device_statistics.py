"""
Device Statistics Service
Handles calculation of device statistics in background threads
"""
import asyncio
import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from concurrent.futures import ThreadPoolExecutor
import re

from database.connection import db_manager

logger = logging.getLogger(__name__)

class DeviceStatisticsService:
    """Service for calculating device statistics in background"""
    
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=4)
        self._stats_cache = {
            'total_devices': 0,
            'online_devices': 0,
            'system_status': 'offline',
            'active_alerts': 0,
            'last_update': None,
            'device_details': {}
        }
        self._lock = threading.Lock()
        self._update_interval = 5  # 5 seconds
        self._running = False
        self._update_thread = None
    
    def start_background_updates(self):
        """Start background statistics updates"""
        if self._running:
            return
        
        self._running = True
        self._update_thread = threading.Thread(target=self._background_update_loop, daemon=True)
        self._update_thread.start()
        logger.info("Device statistics background updates started")
    
    def stop_background_updates(self):
        """Stop background statistics updates"""
        self._running = False
        if self._update_thread:
            self._update_thread.join(timeout=10)
        logger.info("Device statistics background updates stopped")
    
    def _background_update_loop(self):
        """Background loop for updating statistics"""
        while self._running:
            try:
                # Run statistics calculation in thread pool
                future = self.executor.submit(self._calculate_statistics)
                stats = future.result(timeout=30)  # 30 second timeout
                
                with self._lock:
                    self._stats_cache.update(stats)
                    self._stats_cache['last_update'] = datetime.now()
                
                logger.info(f"Statistics updated: Total={stats['total_devices']}, Online={stats['online_devices']}, Status={stats['system_status']}")
                
            except Exception as e:
                logger.error(f"Error updating device statistics: {e}")
                # Set error status
                with self._lock:
                    self._stats_cache['system_status'] = 'error'
            
            # Wait for next update
            time.sleep(self._update_interval)
    
    def _calculate_statistics(self) -> Dict[str, Any]:
        """Calculate device statistics (runs in background thread)"""
        try:
            # Get all tables from database
            tables = self._get_all_tables()
            
            # Calculate total devices
            total_devices = self._calculate_total_devices(tables)
            
            # Calculate online devices
            online_devices, device_details = self._calculate_online_devices(tables)
            
            # Calculate system status
            system_status = self._calculate_system_status(online_devices, total_devices)
            
            # Calculate active alerts (placeholder for now)
            active_alerts = self._calculate_active_alerts(device_details)
            
            return {
                'total_devices': total_devices,
                'online_devices': online_devices,
                'system_status': system_status,
                'active_alerts': active_alerts,
                'device_details': device_details
            }
            
        except Exception as e:
            logger.error(f"Error calculating statistics: {e}")
            return {
                'total_devices': 0,
                'online_devices': 0,
                'system_status': 'error',
                'active_alerts': 1,  # Error alert
                'device_details': {}
            }
    
    def _get_all_tables(self) -> List[str]:
        """Get all tables from database"""
        try:
            with db_manager.sync_engine.connect() as conn:
                from sqlalchemy import text
                result = conn.execute(text("SHOW TABLES"))
                tables = [row[0] for row in result.fetchall()]
                return tables
        except Exception as e:
            logger.error(f"Error getting tables: {e}")
            return []
    
    def _calculate_total_devices(self, tables: List[str]) -> int:
        """Calculate total number of unique devices"""
        device_patterns = {
            'esp32': r'esp32_device_(\d+)_session_',
            'gas': r'gas_device_(\d+)_session_',
            'flow': r'flow_device_(\d+)_session_',
            'shaft': r'shaft_device_(\d+)_session_',
            'rh': r'rh_device_(\d+)_session_',
            'gps': r'gps_data_'  # GPS doesn't have device numbers
        }
        
        unique_devices = set()
        
        for table in tables:
            for device_type, pattern in device_patterns.items():
                if device_type == 'gps':
                    # GPS tables don't have device numbers, count as 1 device
                    if re.match(pattern, table):
                        unique_devices.add(f"{device_type}_1")
                else:
                    match = re.match(pattern, table)
                    if match:
                        device_number = match.group(1)
                        unique_devices.add(f"{device_type}_{device_number}")
        
        return len(unique_devices)
    
    def _calculate_online_devices(self, tables: List[str]) -> tuple[int, Dict[str, Any]]:
        """Calculate number of online devices and return device details"""
        device_patterns = {
            'esp32': r'esp32_device_(\d+)_session_(\d{8}_\d{6})',
            'gas': r'gas_device_(\d+)_session_(\d{8}_\d{6})',
            'flow': r'flow_device_(\d+)_session_(\d{8}_\d{6})',
            'shaft': r'shaft_device_(\d+)_session_(\d{8}_\d{6})',
            'rh': r'rh_device_(\d+)_session_(\d{8}_\d{6})',
            'gps': r'gps_data_(\d{8}_\d{6})'
        }
        
        device_latest_tables = {}
        device_details = {}
        
        # Find latest table for each device
        for table in tables:
            for device_type, pattern in device_patterns.items():
                match = re.match(pattern, table)
                if match:
                    if device_type == 'gps':
                        device_key = f"{device_type}_1"
                        session_time = match.group(1)
                    else:
                        device_number = match.group(1)
                        session_time = match.group(2)
                        device_key = f"{device_type}_{device_number}"
                    
                    # Keep only the latest table for each device
                    if device_key not in device_latest_tables or session_time > device_latest_tables[device_key]['session_time']:
                        device_latest_tables[device_key] = {
                            'table_name': table,
                            'session_time': session_time,
                            'device_type': device_type
                        }
        
        # Check online status for each device
        online_count = 0
        current_time = datetime.now()
        
        for device_key, table_info in device_latest_tables.items():
            try:
                # Get the latest record from the device's latest table
                latest_record = self._get_latest_record(table_info['table_name'], table_info['device_type'])
                
                if latest_record:
                    # Determine if device is online based on last data timestamp
                    is_online = self._is_device_online(latest_record, current_time)

                    # Get the correct timestamp column based on device type
                    timestamp_value = self._get_timestamp_from_record(latest_record, table_info['device_type'])

                    device_details[device_key] = {
                        'device_type': table_info['device_type'],
                        'table_name': table_info['table_name'],
                        'last_timestamp': timestamp_value,
                        'is_online': is_online,
                        'last_data': latest_record
                    }
                    
                    if is_online:
                        online_count += 1
                else:
                    device_details[device_key] = {
                        'device_type': table_info['device_type'],
                        'table_name': table_info['table_name'],
                        'last_timestamp': None,
                        'is_online': False,
                        'last_data': None
                    }
                    
            except Exception as e:
                logger.error(f"Error checking device {device_key}: {e}")
                device_details[device_key] = {
                    'device_type': table_info['device_type'],
                    'table_name': table_info['table_name'],
                    'last_timestamp': None,
                    'is_online': False,
                    'last_data': None,
                    'error': str(e)
                }
        
        return online_count, device_details
    
    def _get_latest_record(self, table_name: str, device_type: str) -> Optional[Dict[str, Any]]:
        """Get the latest record from a device table"""
        try:
            with db_manager.sync_engine.connect() as conn:
                from sqlalchemy import text
                
                # Different query based on device type
                if device_type == 'gps':
                    query = f"SELECT * FROM {table_name} ORDER BY id DESC LIMIT 1"
                else:
                    query = f"SELECT * FROM {table_name} ORDER BY id DESC LIMIT 1"
                
                result = conn.execute(text(query))
                row = result.fetchone()
                
                if row:
                    columns = result.keys()
                    return dict(zip(columns, row))
                
                return None
                
        except Exception as e:
            logger.error(f"Error getting latest record from {table_name}: {e}")
            return None

    def _get_timestamp_from_record(self, record: Dict[str, Any], device_type: str) -> Optional[str]:
        """Get the correct timestamp value from a record based on device type"""
        try:
            # Define timestamp column preferences for each device type
            if device_type == 'gps':
                # GPS uses datetime_rp as primary timestamp
                timestamp_columns = ['datetime_rp', 'created_at', 'timestamp']
            else:
                # Other devices use timestamp as primary
                timestamp_columns = ['timestamp', 'created_at', 'datetime_rp']

            for col in timestamp_columns:
                if col in record and record[col]:
                    timestamp_value = record[col]
                    # Convert to string if it's a datetime object
                    if isinstance(timestamp_value, datetime):
                        return timestamp_value.strftime('%Y-%m-%d %H:%M:%S')
                    elif isinstance(timestamp_value, str):
                        return timestamp_value

            return None

        except Exception as e:
            logger.error(f"Error getting timestamp from record: {e}")
            return None

    def _is_device_online(self, latest_record: Dict[str, Any], current_time: datetime) -> bool:
        """Determine if a device is online based on its latest record"""
        try:
            # Try to get timestamp from different possible columns
            # Check if this might be a GPS record by looking for datetime_rp column
            if 'datetime_rp' in latest_record and latest_record['datetime_rp']:
                # GPS record - prioritize datetime_rp
                timestamp_columns = ['datetime_rp', 'created_at', 'timestamp']
            else:
                # Other device types - prioritize timestamp
                timestamp_columns = ['timestamp', 'created_at', 'datetime_rp']

            timestamp_value = None

            for col in timestamp_columns:
                if col in latest_record and latest_record[col]:
                    timestamp_value = latest_record[col]
                    break
            
            if not timestamp_value:
                return False
            
            # Parse timestamp
            if isinstance(timestamp_value, datetime):
                last_time = timestamp_value
            elif isinstance(timestamp_value, str):
                # Try different timestamp formats
                try:
                    # ISO format
                    last_time = datetime.fromisoformat(timestamp_value.replace('Z', ''))
                except:
                    try:
                        # MySQL datetime format
                        last_time = datetime.strptime(timestamp_value, '%Y-%m-%d %H:%M:%S')
                    except:
                        try:
                            # European format (dd.mm.yyyy hh:mm:ss)
                            last_time = datetime.strptime(timestamp_value, '%d.%m.%Y %H:%M:%S')
                        except:
                            logger.warning(f"Could not parse timestamp: {timestamp_value}")
                            return False
            else:
                return False
            
            # Check if device is online (within 10 minutes)
            time_diff = current_time - last_time
            is_online = time_diff.total_seconds() <= 600  # 10 minutes
            
            return is_online
            
        except Exception as e:
            logger.error(f"Error checking device online status: {e}")
            return False
    
    def _calculate_system_status(self, online_devices: int, total_devices: int) -> str:
        """Calculate overall system status"""
        try:
            # Test database connection
            with db_manager.sync_engine.connect() as conn:
                from sqlalchemy import text
                conn.execute(text("SELECT 1"))
            
            # System is online if database is connected
            if total_devices == 0:
                return 'offline'  # No devices found
            elif online_devices == 0:
                return 'error'  # No devices online
            elif online_devices < total_devices * 0.5:
                return 'warning'  # Less than 50% devices online
            else:
                return 'operational'  # Most devices online
                
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            return 'offline'
    
    def _calculate_active_alerts(self, device_details: Dict[str, Any]) -> int:
        """Calculate number of active alerts"""
        alert_count = 0
        
        # Count offline devices as alerts
        for device_key, details in device_details.items():
            if not details.get('is_online', False):
                alert_count += 1
        
        # Add system alerts if needed
        if self._stats_cache.get('system_status') == 'error':
            alert_count += 1
        
        return alert_count
    
    def get_current_statistics(self) -> Dict[str, Any]:
        """Get current cached statistics"""
        with self._lock:
            return self._stats_cache.copy()

# Global instance
device_stats_service = DeviceStatisticsService()
