import os
from dotenv import load_dotenv

print("Current working directory:", os.getcwd())
print("Config.env exists:", os.path.exists("config.env"))

load_dotenv()

print("MYSQL_HOST:", os.getenv('MYSQL_HOST'))
print("MYSQL_USER:", os.getenv('MYSQL_USER'))
print("MYSQL_DATABASE:", os.getenv('MYSQL_DATABASE'))

# Check if config.env exists and print its contents
if os.path.exists("config.env"):
    print("\nconfig.env contents:")
    with open("config.env", "r", encoding="utf-8") as f:
        print(f.read())
else:
    print("config.env file not found!")
