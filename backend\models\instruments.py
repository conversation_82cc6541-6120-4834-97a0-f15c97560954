"""
仪器数据模型
定义各种仪器的数据结构
"""
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class InstrumentType(str, Enum):
    """仪器类型枚举"""
    GAS_ANALYZER = "gas_analyzer"
    FLOW_METER = "flow_meter"
    SHAFT_POWER = "shaft_power"
    RH_METER = "rh_meter"
    ESP32 = "esp32"
    GPS = "gps"

class InstrumentStatus(str, Enum):
    """仪器状态枚举"""
    ONLINE = "online"
    OFFLINE = "offline"
    ERROR = "error"
    MAINTENANCE = "maintenance"

# 基础仪器数据模型
class BaseInstrumentData(BaseModel):
    """基础仪器数据模型"""
    id: Optional[int] = None
    device_number: int
    timestamp: datetime
    serial_number: Optional[str] = None
    
    class Config:
        from_attributes = True

# Gas Analyzer 数据模型
class GasAnalyzerData(BaseInstrumentData):
    """气体分析仪数据模型"""
    co2: Optional[float] = Field(None, description="CO2浓度 (%)")
    o2: Optional[float] = Field(None, description="O2浓度 (%)")
    co: Optional[float] = Field(None, description="CO浓度 (ppm)")
    nox: Optional[float] = Field(None, description="NOx浓度 (ppm)")
    so2: Optional[float] = Field(None, description="SO2浓度 (ppm)")
    exhaust_temp: Optional[float] = Field(None, description="排气温度 (°C)")
    lambda_value: Optional[float] = Field(None, description="Lambda值")
    efficiency: Optional[float] = Field(None, description="效率 (%)")

# Flow Meter 数据模型
class FlowMeterData(BaseInstrumentData):
    """质量流量计数据模型"""
    mass_flow: Optional[float] = Field(None, description="质量流量 (kg/h)")
    volume_flow: Optional[float] = Field(None, description="体积流量 (L/h)")
    density: Optional[float] = Field(None, description="密度 (kg/m³)")
    temperature: Optional[float] = Field(None, description="温度 (°C)")
    pressure: Optional[float] = Field(None, description="压力 (bar)")
    total_mass: Optional[float] = Field(None, description="累计质量 (kg)")

# Shaft Power Meter 数据模型
class ShaftPowerData(BaseInstrumentData):
    """轴功率计数据模型"""
    power: Optional[float] = Field(None, description="功率 (kW)")
    torque: Optional[float] = Field(None, description="扭矩 (N·m)")
    speed: Optional[float] = Field(None, description="转速 (rpm)")
    thrust: Optional[float] = Field(None, description="推力 (kN)")
    slip: Optional[float] = Field(None, description="滑脱率 (%)")

# RH Meter 数据模型
class RHMeterData(BaseInstrumentData):
    """温湿度计数据模型"""
    temperature: Optional[float] = Field(None, description="温度 (°C)")
    humidity: Optional[float] = Field(None, description="相对湿度 (%)")
    dew_point: Optional[float] = Field(None, description="露点温度 (°C)")

# ESP32 数据模型
class ESP32Data(BaseInstrumentData):
    """ESP32数据模型"""
    temperature: Optional[float] = Field(None, description="温度 (°C)")
    humidity: Optional[float] = Field(None, description="相对湿度 (%)")
    esp32_time: Optional[datetime] = Field(None, description="ESP32时间")

# 仪器信息模型
class InstrumentInfo(BaseModel):
    """仪器信息模型"""
    instrument_type: InstrumentType
    device_number: int
    serial_number: Optional[str] = None
    status: InstrumentStatus = InstrumentStatus.OFFLINE
    last_update: Optional[datetime] = None
    location: Optional[str] = None
    description: Optional[str] = None

# 仪器统计数据模型
class InstrumentStats(BaseModel):
    """仪器统计数据模型"""
    instrument_type: InstrumentType
    device_number: int
    total_records: int
    latest_timestamp: Optional[datetime] = None
    oldest_timestamp: Optional[datetime] = None
    avg_interval: Optional[float] = Field(None, description="平均数据间隔 (秒)")

# 操作面板数据模型
class OperationProfileData(BaseModel):
    """操作面板数据模型"""
    timestamp: datetime
    instruments: Dict[str, List[Dict[str, Any]]] = Field(
        default_factory=dict,
        description="各仪器的最新数据"
    )
    system_status: str = "operational"
    alerts: List[Dict[str, Any]] = Field(default_factory=list)
    
    class Config:
        json_schema_extra = {
            "example": {
                "timestamp": "2024-01-01T12:00:00Z",
                "instruments": {
                    "gas_analyzer": [
                        {
                            "device_number": 1,
                            "co2": 3.2,
                            "o2": 14.5,
                            "timestamp": "2024-01-01T12:00:00Z"
                        }
                    ],
                    "flow_meter": [
                        {
                            "device_number": 1,
                            "mass_flow": 125.5,
                            "temperature": 45.2,
                            "timestamp": "2024-01-01T12:00:00Z"
                        }
                    ]
                },
                "system_status": "operational",
                "alerts": []
            }
        }

# 数据查询参数模型
class DataQueryParams(BaseModel):
    """数据查询参数模型"""
    instrument_type: InstrumentType
    device_number: Optional[int] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    limit: int = Field(default=1000, ge=1, le=10000, description="返回记录数限制")
    offset: int = Field(default=0, ge=0, description="偏移量")
    
# API响应模型
class APIResponse(BaseModel):
    """API响应模型"""
    success: bool = True
    message: str = "Success"
    data: Optional[Any] = None
    total: Optional[int] = None
    timestamp: datetime = Field(default_factory=datetime.now)

class ErrorResponse(BaseModel):
    """错误响应模型"""
    success: bool = False
    message: str
    error_code: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)
