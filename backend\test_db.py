"""
Test database connection
"""
import asyncio
from database.connection import DatabaseManager

async def test_database():
    try:
        print("Testing database connection...")
        dm = DatabaseManager()
        await dm.initialize()
        print("Database connected successfully!")
        
        # Test getting table list
        tables = await dm.get_table_list()
        print(f"Found {len(tables)} tables in database")
        
        if tables:
            print("First 10 tables:")
            for i, table in enumerate(tables[:10]):
                print(f"  {i+1}. {table}")
        
        await dm.close()
        print("Database connection closed successfully")
        
    except Exception as e:
        print(f"Database connection failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_database())
