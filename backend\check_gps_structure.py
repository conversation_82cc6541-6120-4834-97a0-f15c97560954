#!/usr/bin/env python3
"""
Check GPS table structure and data
"""
import asyncio
import sys
import os

# Add the backend directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database.connection import db_manager

async def check_gps_structure():
    """Check GPS table structure and data"""
    try:
        print("Checking GPS table structure...")
        
        # Initialize database manager
        await db_manager.initialize()
        
        # Get GPS tables
        instrument_tables = await db_manager.get_instrument_tables()
        gps_tables = instrument_tables.get('gps', [])
        
        print(f"Found {len(gps_tables)} GPS tables:")
        for table in gps_tables:
            print(f"  - {table}")
        
        # Check structure of each GPS table
        for table in gps_tables[:3]:  # Check first 3 tables
            print(f"\n📊 Checking table: {table}")
            
            # Get table structure
            try:
                describe_query = f"DESCRIBE {table}"
                columns = await db_manager.execute_query(describe_query)
                
                print(f"  Columns ({len(columns)}):")
                for col in columns:
                    print(f"    - {col['Field']} ({col['Type']})")
                
                # Get sample data
                sample_query = f"SELECT * FROM {table} ORDER BY id DESC LIMIT 3"
                sample_data = await db_manager.execute_query(sample_query)
                
                print(f"  Sample records ({len(sample_data)}):")
                for i, record in enumerate(sample_data):
                    print(f"    Record {i+1}:")
                    for key, value in record.items():
                        if value is not None and str(value).strip():
                            print(f"      {key}: {value}")
                
            except Exception as e:
                print(f"    ❌ Error checking table {table}: {e}")
        
        # Close connection
        await db_manager.close()
        print("\n✅ GPS structure check completed")
        
    except Exception as e:
        print(f"❌ GPS structure check failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(check_gps_structure())

