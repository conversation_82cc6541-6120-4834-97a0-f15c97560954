"""
Configuration Management Module
Handles environment variables and configuration files
"""
import os
import json
from pathlib import Path

def load_mysql_config():
    """
    Load MySQL configuration
    Priority: Environment variables > config.env > Project config file > Default values
    """
    config = {}
    
    # 1. 尝试从环境变量加载
    if all(key in os.environ for key in ['MYSQL_HOST', 'MYSQL_USER', 'MYSQL_PASSWORD', 'MYSQL_DATABASE']):
        config = {
            'host': os.getenv('MYSQL_HOST'),
            'port': int(os.getenv('MYSQL_PORT', 3306)),
            'user': os.getenv('MYSQL_USER'),
            'password': os.getenv('MYSQL_PASSWORD'),
            'database': os.getenv('MYSQL_DATABASE')
        }
        print("✅ 使用环境变量中的MySQL配置")
        return config
    
    # 2. 尝试从config.env文件加载
    config_env_path = Path(__file__).parent / "config.env"
    if config_env_path.exists():
        try:
            with open(config_env_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
            
            if all(key in os.environ for key in ['MYSQL_HOST', 'MYSQL_USER', 'MYSQL_PASSWORD', 'MYSQL_DATABASE']):
                config = {
                    'host': os.environ['MYSQL_HOST'],
                    'port': int(os.environ.get('MYSQL_PORT', 3306)),
                    'user': os.environ['MYSQL_USER'],
                    'password': os.environ['MYSQL_PASSWORD'],
                    'database': os.environ['MYSQL_DATABASE']
                }
                print("✅ 使用config.env文件中的MySQL配置")
                return config
        except Exception as e:
            print(f"❌ 读取config.env文件失败: {e}")
    
    # 3. 尝试从项目配置文件加载
    project_config_path = Path(__file__).parent.parent.parent / "config" / "mysql_config.json"
    if project_config_path.exists():
        try:
            with open(project_config_path, 'r', encoding='utf-8') as f:
                project_config = json.load(f)
                config = {
                    'host': project_config.get('host', 'localhost'),
                    'port': int(project_config.get('port', 3306)),
                    'user': project_config.get('username', ''),
                    'password': project_config.get('password', ''),
                    'database': project_config.get('database', 'eepm_data')
                }
                print("✅ 使用项目配置文件中的MySQL配置")
                return config
        except Exception as e:
            print(f"❌ 读取项目配置文件失败: {e}")
    
    # 4. 使用默认配置
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '',
        'database': 'eepm_data'
    }
    print("⚠️  使用默认MySQL配置，请检查连接参数")
    
    return config

def create_sample_config():
    """创建示例配置文件"""
    config_content = """# EEPM Data Metrics - 后端配置文件
# 复制此文件为 config.env 并修改相应参数

# MySQL数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=eepm_data

# API服务配置
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=True

# 安全配置
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS配置
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# 数据更新配置
AUTO_UPDATE_INTERVAL=30
DATA_CACHE_SIZE=1000
"""
    
    config_path = Path(__file__).parent / "config.env.example"
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print(f"✅ 已创建示例配置文件: {config_path}")
    print("请复制为 config.env 并修改相应参数")

if __name__ == "__main__":
    print("EEPM Data Metrics - 配置管理")
    print("=" * 40)
    
    # 创建示例配置
    create_sample_config()
    
    print("\n测试MySQL配置加载:")
    config = load_mysql_config()
    
    print("\n当前配置:")
    for key, value in config.items():
        if key == 'password':
            print(f"  {key}: {'*' * len(str(value))}")
        else:
            print(f"  {key}: {value}")
    
    print("\n配置优先级:")
    print("1. 环境变量")
    print("2. config.env 文件")
    print("3. 项目配置文件 (../../config/mysql_config.json)")
    print("4. 默认值")
