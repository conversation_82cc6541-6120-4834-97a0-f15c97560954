"""
Database Connection Manager
Connects to AWS MySQL database for instrument data
"""
import os
import asyncio
from typing import Optional, Dict, List, Any
from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
import pymysql
import logging
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseManager:
    """Database Manager for handling MySQL connections"""
    
    def __init__(self):
        self.sync_engine = None
        self.async_engine = None
        self.async_session_factory = None
        self._connection_string = None
        self._async_connection_string = None
        
    async def initialize(self):
        """Initialize database connection"""
        try:
            # Load database configuration from environment variables or config files
            mysql_config = self._load_mysql_config()
            
            # Build connection string
            self._connection_string = (
                f"mysql+pymysql://{mysql_config['user']}:{mysql_config['password']}"
                f"@{mysql_config['host']}:{mysql_config['port']}/{mysql_config['database']}"
                f"?charset=utf8mb4"
            )
            
            self._async_connection_string = (
                f"mysql+aiomysql://{mysql_config['user']}:{mysql_config['password']}"
                f"@{mysql_config['host']}:{mysql_config['port']}/{mysql_config['database']}"
                f"?charset=utf8mb4"
            )
            
            # Create synchronous engine for simple queries
            self.sync_engine = create_engine(
                self._connection_string,
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True,
                echo=False
            )
            
            # Create asynchronous engine for heavy data queries
            # self.async_engine = create_async_engine(
            #     self._async_connection_string,
            #     pool_size=10,
            #     max_overflow=20,
            #     pool_pre_ping=True,
            #     echo=False
            # )
            
            # self.async_session_factory = sessionmaker(
            #     self.async_engine,
            #     class_=AsyncSession,
            #     expire_on_commit=False
            # )
            
            # Test connection
            await self._test_connection()
            logger.info("Database connection initialized successfully")
            
        except Exception as e:
            logger.error(f"Database connection initialization failed: {e}")
            raise
    
    def _load_mysql_config(self) -> Dict[str, str]:
        """Load MySQL configuration"""
        # Load from config.env file first (try different paths)
        config_paths = ["config.env", "../config.env", "./config.env"]
        for config_path in config_paths:
            if os.path.exists(config_path):
                load_dotenv(config_path)
                logger.info(f"Loaded config from: {config_path}")
                break
        
        # Priority: environment variables
        if all(key in os.environ for key in ['MYSQL_HOST', 'MYSQL_USER', 'MYSQL_PASSWORD', 'MYSQL_DATABASE']):
            config = {
                'host': os.getenv('MYSQL_HOST'),
                'port': int(os.getenv('MYSQL_PORT', 3306)),
                'user': os.getenv('MYSQL_USER'),
                'password': os.getenv('MYSQL_PASSWORD'),
                'database': os.getenv('MYSQL_DATABASE')
            }
            logger.info(f"Using MySQL config: {config['host']}:{config['port']}/{config['database']}")
            return config
        
        # Try to read from project config file
        try:
            import json
            config_paths = ["../../config/mysql_config.json", "../../../config/mysql_config.json"]
            for config_path in config_paths:
                if os.path.exists(config_path):
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                        result = {
                            'host': config.get('host', 'localhost'),
                            'port': int(config.get('port', 3306)),
                            'user': config.get('username', ''),
                            'password': config.get('password', ''),
                            'database': config.get('database', 'eepm_data')
                        }
                        logger.info(f"Using JSON config from: {config_path}")
                        return result
        except Exception as e:
            logger.warning(f"Unable to read config file: {e}")
        
        # Default configuration
        logger.warning("Using default MySQL configuration")
        return {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '',
            'database': 'eepm_data'
        }
    
    async def _test_connection(self):
        """Test database connection"""
        try:
            with self.sync_engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                result.fetchone()
            logger.info("Database connection test successful")
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            raise
    
    async def close(self):
        """Close database connection"""
        try:
            if self.sync_engine:
                self.sync_engine.dispose()
            if self.async_engine:
                await self.async_engine.dispose()
            logger.info("Database connection closed")
        except Exception as e:
            logger.error(f"Error closing database connection: {e}")
    
    def get_sync_connection(self):
        """Get synchronous database connection"""
        return self.sync_engine.connect()
    
    async def execute_query(self, query: str, params: Dict = None) -> List[Dict]:
        """Execute query and return results"""
        try:
            with self.sync_engine.connect() as conn:
                if params:
                    result = conn.execute(text(query), params)
                else:
                    result = conn.execute(text(query))
                
                # Convert results to dictionary list
                columns = result.keys()
                rows = result.fetchall()
                
                return [dict(zip(columns, row)) for row in rows]
                
        except Exception as e:
            logger.error(f"Query execution failed: {e}")
            raise
    
    async def get_table_list(self) -> List[str]:
        """Get list of all tables in database"""
        try:
            query = "SHOW TABLES"
            result = await self.execute_query(query)
            tables = [list(row.values())[0] for row in result]
            return tables
        except Exception as e:
            logger.error(f"Failed to get table list: {e}")
            return []
    
    async def get_instrument_tables(self) -> Dict[str, List[str]]:
        """Get data tables for each instrument"""
        try:
            tables = await self.get_table_list()
            
            instrument_tables = {
                'gas_analyzer': [t for t in tables if 'gas_analyzer' in t.lower() or 'gas_device' in t.lower()],
                'flow_meter': [t for t in tables if 'flow_meter' in t.lower() or 'flow_device' in t.lower()],
                'shaft_power': [t for t in tables if 'shaft_power' in t.lower() or 'shaft_device' in t.lower()],
                'rh_meter': [t for t in tables if 'rh_meter' in t.lower() or 'rh_device' in t.lower()],
                'esp32': [t for t in tables if 'esp32' in t.lower()],
                'gps': [t for t in tables if 'gps_data' in t.lower() or t.lower().startswith('gps_')]
            }
            
            return instrument_tables
            
        except Exception as e:
            logger.error(f"Failed to get instrument tables: {e}")
            return {}

# Global database manager instance
db_manager = DatabaseManager()
