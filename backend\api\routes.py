"""
API Route Definitions
Contains all API endpoints
"""
from fastapi import APIRouter, HTTPException, Query, Depends
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import logging
from sqlalchemy import text

logger = logging.getLogger(__name__)

from models.instruments import (
    InstrumentType, 
    OperationProfileData, 
    DataQueryParams, 
    APIResponse,
    InstrumentInfo,
    InstrumentStats
)
# Commented out to disable database connections
# from api.instruments import (
#     gas_analyzer_router,
#     flow_meter_router,
#     shaft_power_router,
#     rh_meter_router,
#     esp32_router
# )
from database.connection import db_manager
from services.device_statistics import device_stats_service

# 创建主路由器
router = APIRouter()

# 包含各仪器的子路由 - 已禁用数据库连接
# router.include_router(gas_analyzer_router, prefix="/gas-analyzer", tags=["Gas Analyzer"])
# router.include_router(flow_meter_router, prefix="/flow-meter", tags=["Flow Meter"])
# router.include_router(shaft_power_router, prefix="/shaft-power", tags=["Shaft Power"])
# router.include_router(rh_meter_router, prefix="/rh-meter", tags=["RH Meter"])
# router.include_router(esp32_router, prefix="/esp32", tags=["ESP32"])

@router.get("/", response_model=APIResponse)
async def api_root():
    """API根端点"""
    return APIResponse(
        message="EEPM Data Metrics API v1.0",
        data={
            "version": "1.0.0",
            "endpoints": [
                "/operation-profile",
                "/instruments",
                "/gas-analyzer",
                "/flow-meter", 
                "/shaft-power",
                "/rh-meter",
                "/esp32",
                "/gps"
            ]
        }
    )

@router.get("/health")
async def health_check():
    """Health check endpoint for frontend"""
    try:
        # Return healthy status without database connection
        return {
            "status": "healthy",
            "database": "disconnected",
            "tables_count": 0,
            "message": "Database connections disabled",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Health check failed: {str(e)}")

@router.get("/device-statistics")
async def get_device_statistics():
    """Get device statistics for dashboard"""
    try:
        stats = device_stats_service.get_current_statistics()
        return {
            "total_devices": stats.get('total_devices', 0),
            "online_devices": stats.get('online_devices', 0),
            "system_status": stats.get('system_status', 'offline'),
            "active_alerts": stats.get('active_alerts', 0),
            "last_update": stats.get('last_update').isoformat() if stats.get('last_update') else None,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Error getting device statistics: {e}")
        return {
            "total_devices": 0,
            "online_devices": 0,
            "system_status": "error",
            "active_alerts": 1,
            "last_update": None,
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }

@router.get("/device-details")
async def get_device_details():
    """Get detailed device status information"""
    try:
        stats = device_stats_service.get_current_statistics()
        device_details = stats.get('device_details', {})

        # Format device details for frontend
        devices = []
        for device_key, details in device_details.items():
            # Parse device key to get type and number
            parts = device_key.split('_')
            device_type = parts[0] if len(parts) > 0 else 'unknown'
            device_number = parts[1] if len(parts) > 1 else '1'

            # Format last timestamp
            last_timestamp = details.get('last_timestamp')
            if last_timestamp and isinstance(last_timestamp, str):
                try:
                    # Try to parse and format timestamp
                    from datetime import datetime
                    if 'T' in last_timestamp:
                        dt = datetime.fromisoformat(last_timestamp.replace('Z', ''))
                    else:
                        dt = datetime.strptime(last_timestamp, '%Y-%m-%d %H:%M:%S')
                    formatted_timestamp = dt.strftime('%Y-%m-%d %H:%M:%S')
                except:
                    formatted_timestamp = str(last_timestamp)
            else:
                formatted_timestamp = 'No data'

            device_info = {
                "device_key": device_key,
                "device_type": device_type.upper(),
                "device_number": device_number,
                "is_online": details.get('is_online', False),
                "status": "Online" if details.get('is_online', False) else "Offline",
                "last_timestamp": formatted_timestamp,
                "table_name": details.get('table_name', 'N/A'),
                "error": details.get('error')
            }
            devices.append(device_info)

        # Sort devices by type and number
        devices.sort(key=lambda x: (x['device_type'], int(x['device_number']) if x['device_number'].isdigit() else 0))

        return {
            "devices": devices,
            "total_count": len(devices),
            "online_count": sum(1 for d in devices if d['is_online']),
            "offline_count": sum(1 for d in devices if not d['is_online']),
            "last_update": stats.get('last_update').isoformat() if stats.get('last_update') else None,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error getting device details: {e}")
        return {
            "devices": [],
            "total_count": 0,
            "online_count": 0,
            "offline_count": 0,
            "last_update": None,
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }

@router.get("/latest-device-data")
async def get_latest_device_data():
    """Get latest data from all device types for Operation Profile"""
    try:
        # Get device statistics to know which devices are online
        stats = device_stats_service.get_current_statistics()
        device_details = stats.get('device_details', {})

        result = {
            "gps": None,
            "gas": None,
            "flow": None,
            "shaft": None,
            "esp32": None,
            "timestamp": datetime.now().isoformat()
        }

        # Get latest data for each device type (device 1 only)
        device_types = ['gps', 'gas', 'flow', 'shaft', 'esp32']

        for device_type in device_types:
            device_key = f"{device_type}_1"
            device_info = device_details.get(device_key)

            logger.info(f"Looking for device: {device_key}, found: {device_info is not None}")
            if device_info:
                logger.info(f"Device info: {device_info}")

            if device_info and device_info.get('table_name'):
                table_name = device_info['table_name']
                logger.info(f"Getting latest data from table: {table_name}")
                latest_data = await _get_latest_device_record(table_name, device_type)

                if latest_data:
                    # Add device status information
                    latest_data['is_online'] = device_info.get('is_online', False)
                    latest_data['device_type'] = device_type
                    latest_data['table_name'] = table_name

                    result[device_type] = latest_data
                    logger.info(f"Successfully got data for {device_type}")
                else:
                    logger.warning(f"No latest data found for {device_type} from table {table_name}")
            else:
                # Device not found or no table
                logger.warning(f"Device {device_key} not found or no table name")
                result[device_type] = {
                    'is_online': False,
                    'device_type': device_type,
                    'error': 'Device not found or no data table'
                }

        return result

    except Exception as e:
        logger.error(f"Error getting latest device data: {e}")
        return {
            "gps": None,
            "gas": None,
            "flow": None,
            "shaft": None,
            "esp32": None,
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }

async def _get_latest_device_record(table_name: str, device_type: str) -> Optional[Dict[str, Any]]:
    """Get the latest record from a device table"""
    try:
        with db_manager.sync_engine.connect() as conn:
            from sqlalchemy import text

            if device_type == 'flow':
                # Flow meter needs special processing - get latest 2 records and parse them
                logger.info(f"Querying flow table: {table_name}")

                try:
                    query = f"SELECT * FROM {table_name} ORDER BY id DESC LIMIT 2"
                    result = conn.execute(text(query))
                    rows = result.fetchall()

                    logger.info(f"Flow query returned {len(rows) if rows else 0} rows")

                    if rows:
                        columns = result.keys()
                        records = [dict(zip(columns, row)) for row in rows]

                        # Process flow data - parse Channel A and B from value field
                        channel_a = None
                        channel_b = None

                        logger.info(f"Processing {len(records)} flow records")
                        for record in records:
                            value_str = str(record.get('value', ''))
                            logger.info(f"Flow record ID {record.get('id')}: value='{value_str}'")

                            if value_str.startswith('A:'):
                                # Parse Channel A: 'A:;    70,57;  +42474,524;    -490,670;  35,24;'
                                parts = value_str.split(';')
                                logger.info(f"Channel A parts: {parts}")

                                volumetric_flow = None
                                temperature = None

                                if len(parts) >= 2:
                                    try:
                                        # Second part after 'A:;' is volumetric flow
                                        flow_str = parts[1].strip().replace(',', '.')
                                        volumetric_flow = float(flow_str)
                                        logger.info(f"Parsed Channel A flow: {volumetric_flow}")
                                    except (ValueError, IndexError) as e:
                                        logger.warning(f"Failed to parse Channel A flow: {e}")

                                if len(parts) >= 5:
                                    try:
                                        # Fifth part is temperature for Channel A
                                        temp_str = parts[4].strip().replace(',', '.')
                                        temperature = float(temp_str)
                                        logger.info(f"Parsed Channel A temperature: {temperature}")
                                    except (ValueError, IndexError) as e:
                                        logger.warning(f"Failed to parse Channel A temperature: {e}")

                                channel_a = {
                                    'channel': 'A',
                                    'volumetric_flow': volumetric_flow,
                                    'temperature': temperature,
                                    'timestamp': record.get('timestamp')
                                }

                            elif value_str.startswith('B:'):
                                # Parse Channel B: 'B:;   213,92;  +51737,463;      -1,826;'
                                parts = value_str.split(';')
                                logger.info(f"Channel B parts: {parts}")

                                volumetric_flow = None

                                if len(parts) >= 2:
                                    try:
                                        # Second part after 'B:;' is volumetric flow
                                        flow_str = parts[1].strip().replace(',', '.')
                                        volumetric_flow = float(flow_str)
                                        logger.info(f"Parsed Channel B flow: {volumetric_flow}")
                                    except (ValueError, IndexError) as e:
                                        logger.warning(f"Failed to parse Channel B flow: {e}")

                                channel_b = {
                                    'channel': 'B',
                                    'volumetric_flow': volumetric_flow,
                                    'temperature': None,  # Channel B doesn't have temperature
                                    'timestamp': record.get('timestamp')
                                }

                        # Process timestamp format for display
                        display_timestamp = None
                        raw_timestamp = None

                        # Get timestamp from any available record
                        if channel_a and channel_a.get('timestamp'):
                            raw_timestamp = channel_a.get('timestamp')
                        elif channel_b and channel_b.get('timestamp'):
                            raw_timestamp = channel_b.get('timestamp')
                        elif records:
                            # If no channel data parsed, get timestamp from first record
                            raw_timestamp = records[0].get('timestamp')
                            logger.info(f"Using timestamp from first record: {raw_timestamp}")

                        if raw_timestamp:
                            try:
                                # Convert from '18.09.2025 10:24:01' to '2025-09-18 10:24:01'
                                from datetime import datetime
                                dt = datetime.strptime(str(raw_timestamp), '%d.%m.%Y %H:%M:%S')
                                display_timestamp = dt.strftime('%Y-%m-%d %H:%M:%S')
                                logger.info(f"Converted flow timestamp from '{raw_timestamp}' to '{display_timestamp}'")
                            except ValueError as e:
                                logger.warning(f"Failed to parse flow timestamp '{raw_timestamp}': {e}")
                                display_timestamp = str(raw_timestamp)  # Fallback to original

                        # Return combined data with both channels
                        result_data = {
                            'channel_a': channel_a,
                            'channel_b': channel_b,
                            'display_timestamp': display_timestamp
                        }

                        logger.info(f"Flow data result: {result_data}")
                        return result_data

                    else:
                        logger.warning(f"No data found in flow table: {table_name}")
                        return {
                            'channel_a': None,
                            'channel_b': None,
                            'display_timestamp': None,
                            'error': 'No flow data available'
                        }

                except Exception as e:
                    logger.error(f"Error querying flow table {table_name}: {e}")
                    return {
                        'channel_a': None,
                        'channel_b': None,
                        'display_timestamp': None,
                        'error': f'Flow query error: {str(e)}'
                    }
            else:
                # Other device types - get single latest record
                query = f"SELECT * FROM {table_name} ORDER BY id DESC LIMIT 1"
                result = conn.execute(text(query))
                row = result.fetchone()

                if row:
                    columns = result.keys()
                    record = dict(zip(columns, row))

                    # Add timestamp field based on device type
                    if device_type == 'gps':
                        record['display_timestamp'] = record.get('datetime_rp')
                    else:
                        record['display_timestamp'] = record.get('timestamp')

                    return record

            return None

    except Exception as e:
        logger.error(f"Error getting latest record from {table_name}: {e}")
        return None

@router.get("/operation-profile", response_model=OperationProfileData)
async def get_operation_profile():
    """
    Get operation profile data
    Display latest data and system status for all instruments
    """
    try:
        # Get current device statistics
        stats = device_stats_service.get_current_statistics()

        # Return empty data for all instrument types
        instruments_data = {
            "gas_analyzer": [],
            "flow_meter": [],
            "shaft_power": [],
            "rh_meter": [],
            "esp32": [],
            "gps": []
        }

        # Generate alerts based on device statistics
        alerts = []
        device_details = stats.get('device_details', {})

        # Add alerts for offline devices
        for device_key, details in device_details.items():
            if not details.get('is_online', False):
                device_type = details.get('device_type', 'unknown')
                alerts.append({
                    "type": "warning",
                    "instrument": device_type,
                    "message": f"Device {device_key} is offline",
                    "timestamp": datetime.now().isoformat()
                })

        # Add system status alert if needed
        system_status = stats.get('system_status', 'unknown')
        if system_status == 'error':
            alerts.append({
                "type": "error",
                "instrument": "system",
                "message": "System error detected",
                "timestamp": datetime.now().isoformat()
            })
        elif system_status == 'warning':
            alerts.append({
                "type": "warning",
                "instrument": "system",
                "message": "System warning: Some devices are offline",
                "timestamp": datetime.now().isoformat()
            })

        return OperationProfileData(
            timestamp=datetime.now(),
            instruments=instruments_data,
            system_status=system_status,
            alerts=alerts
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get operation profile data: {str(e)}")

async def get_latest_instrument_data(instrument_type: str, limit: int = 5) -> List[Dict[str, Any]]:
    """Get latest data for specified instrument type"""
    # Return empty data - no database connections
    return []

@router.get("/instruments", response_model=List[InstrumentInfo])
async def get_instruments():
    """Get all instrument information"""
    try:
        # Return empty list - no database connections
        instruments = []
        return instruments
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取仪器列表失败: {str(e)}")

@router.get("/instruments/stats", response_model=List[InstrumentStats])
async def get_instruments_stats():
    """获取所有仪器的统计信息"""
    try:
        # Return empty list - no database connections
        stats = []
        return stats
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

@router.get("/database/tables")
async def get_database_tables():
    """获取数据库表信息（调试用）"""
    try:
        # Return empty data - no database connections
        return APIResponse(
            data={
                "all_tables": [],
                "instrument_tables": {},
                "total_tables": 0,
                "message": "Database connections disabled"
            }
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取数据库表信息失败: {str(e)}")

# GPS specific endpoints
@router.get("/gps/latest", response_model=List[Dict[str, Any]])
async def get_gps_latest(limit: int = 1):
    """Get latest GPS data from the most recent GPS table"""
    try:
        # Get all GPS tables from database
        all_tables = await db_manager.execute_query("SHOW TABLES")
        
        # Filter GPS tables with date format: gps_data_YYYYMMDD_HHMMSS
        gps_tables = []
        for table_row in all_tables:
            table_name = list(table_row.values())[0]
            if table_name.startswith('gps_data_') and len(table_name) > 20:
                gps_tables.append(table_name)
        
        if not gps_tables:
            logger.warning("No GPS tables found")
            return []
        
        # Sort GPS tables by name to find the most recent one (format: gps_data_YYYYMMDD_HHMMSS)
        gps_tables.sort(reverse=True)  # Latest first
        latest_table = gps_tables[0]
        
        logger.info(f"Using latest GPS table: {latest_table}")
        
        # Get the latest record from the most recent GPS table
        query = f"""
        SELECT id, datetime_rp, class, device, mode, time, altHAE, altMSL, sep, eph, epv, 
               geoidSep, lat, leapseconds, lon, velD, velE, velN, created_at
        FROM {latest_table} 
        ORDER BY id DESC 
        LIMIT {limit}
        """
        
        data = await db_manager.execute_query(query)
        
        if data:
            # Log the GPS coordinates for debugging
            logger.info(f"GPS data found: lat={data[0].get('lat')}, lon={data[0].get('lon')}, datetime_rp={data[0].get('datetime_rp')}")
            logger.info(f"GPS data all fields: {list(data[0].keys())}")
            logger.info(f"GPS datetime_rp raw type: {type(data[0].get('datetime_rp'))}")
        
        # Convert datetime objects to ISO strings
        # Note: datetime_rp is already in local time (Singapore), not UTC
        for row in data:
            for key, value in row.items():
                if isinstance(value, datetime):
                    # Don't add 'Z' suffix since these are local timestamps, not UTC
                    row[key] = value.isoformat()
        
        return data
        
    except Exception as e:
        logger.error(f"Failed to get latest GPS data: {e}")
        # Return empty data instead of raising exception to avoid frontend errors
        return []

@router.get("/gps/tables")
async def get_gps_tables():
    """Get available GPS tables information"""
    try:
        # Get all GPS tables from database
        all_tables = await db_manager.execute_query("SHOW TABLES")
        
        # Filter GPS tables with date format: gps_data_YYYYMMDD_HHMMSS
        gps_tables = []
        for table_row in all_tables:
            table_name = list(table_row.values())[0]
            if table_name.startswith('gps_data_') and len(table_name) > 20:
                # Get table info
                count_query = f"SELECT COUNT(*) as count FROM {table_name}"
                count_result = await db_manager.execute_query(count_query)
                record_count = count_result[0]['count'] if count_result else 0
                
                gps_tables.append({
                    "table_name": table_name,
                    "record_count": record_count,
                    "created_date": table_name.split('_')[2] if len(table_name.split('_')) > 2 else "unknown",
                    "created_time": table_name.split('_')[3] if len(table_name.split('_')) > 3 else "unknown"
                })
        
        # Sort by table name (which includes date/time) - latest first
        gps_tables.sort(key=lambda x: x['table_name'], reverse=True)
        
        return {
            "total_tables": len(gps_tables),
            "tables": gps_tables,
            "latest_table": gps_tables[0]['table_name'] if gps_tables else None
        }
        
    except Exception as e:
        logger.error(f"Failed to get GPS tables info: {e}")
        return {
            "total_tables": 0,
            "tables": [],
            "latest_table": None,
            "error": str(e)
        }

@router.get("/gps/history", response_model=List[Dict[str, Any]])
async def get_gps_history(start_time: str = None, end_time: str = None, limit: int = 100):
    """Get GPS historical data"""
    # Return empty data - no database connections
    return []

async def get_instrument_history(instrument_type: str, start_time: str = None, end_time: str = None, limit: int = 100) -> List[Dict[str, Any]]:
    """Get historical data for specified instrument type with time range"""
    # Return empty data - no database connections
    return []

# Gas Analyzer specific endpoints
@router.get("/gas-analyzer/latest")
async def get_gas_analyzer_latest(limit: int = 10):
    """Get latest gas analyzer data from the most recent gas device table"""
    try:
        # Get all Gas Analyzer tables from database
        all_tables = await db_manager.execute_query("SHOW TABLES")
        
        # Filter gas device 1 tables with date format: gas_device_1_session_YYYYMMDD_HHMMSS
        gas_tables = []
        for table_row in all_tables:
            table_name = list(table_row.values())[0]
            if table_name.startswith('gas_device_1_session_') and len(table_name) > 30:
                gas_tables.append(table_name)
        
        if not gas_tables:
            logger.warning("No Gas Analyzer tables found")
            return []
        
        # Sort gas tables by name to find the most recent one
        gas_tables.sort(reverse=True)  # Latest first
        latest_table = gas_tables[0]
        
        logger.info(f"Using latest Gas Analyzer table: {latest_table}")
        
        # First, get the table structure to see what columns exist
        try:
            describe_query = f"DESCRIBE {latest_table}"
            columns_info = await db_manager.execute_query(describe_query)
            available_columns = [col['Field'] for col in columns_info]
            logger.info(f"Available columns in {latest_table}: {available_columns}")
            
            # Build query with only existing columns
            base_columns = ['id']
            optional_columns = [
                'device_number', 'timestamp', 'created_at', 'serial_number',
                'mbar_deltap1', 'm_s_Vel', 'O2_percent', 'CO_ppm', 'NOx_ppm', 
                'NO_ppm', 'NO2_ppm', 'SO2_ppm', 'H2_ppm', 'CO2_IR_percent',
                'O2_ref_dot_percent', 'Tstack_C', 'pump_flow_lmin', 'Tamb_C',
                'CO_emission_lb_h', 'f_m_Flow', 'kg_h_MCO', 'kg_h_MNOx',
                'kg_h_MSO2', 'kg_h_MCO2IR', 'fuel_type', 'CO2_max_percent', 'O2_ref_percent'
            ]
            
            # Only include columns that actually exist in the table
            selected_columns = base_columns + [col for col in optional_columns if col in available_columns]
            
            query = f"""
            SELECT {', '.join(selected_columns)}
            FROM {latest_table} 
            ORDER BY id DESC 
            LIMIT {limit}
            """
            
            logger.info(f"Executing query with available columns: {selected_columns}")
            
        except Exception as describe_error:
            logger.warning(f"Could not describe table structure: {describe_error}")
            # Fallback: try with minimal columns
            query = f"""
            SELECT *
            FROM {latest_table} 
            ORDER BY id DESC 
            LIMIT {limit}
            """
        
        data = await db_manager.execute_query(query)
        
        if data:
            # Log the gas analyzer data for debugging
            logger.info(f"Gas Analyzer data found from {latest_table}: {len(data)} records")
            logger.info(f"Sample data keys: {list(data[0].keys())}")
            
            # Log specific gas values if they exist
            sample_data = data[0]
            gas_values = {}
            for key in ['O2_percent', 'CO_ppm', 'NOx_ppm', 'timestamp']:
                if key in sample_data:
                    gas_values[key] = sample_data[key]
            logger.info(f"Gas values: {gas_values}")
        
        # Convert datetime objects to ISO strings and add table info
        for row in data:
            for key, value in row.items():
                if isinstance(value, datetime):
                    # Don't add 'Z' suffix since these are local timestamps, not UTC
                    row[key] = value.isoformat()
            # Add source table information
            row['source_table'] = latest_table
        
        return data
        
    except Exception as e:
        logger.error(f"Failed to get latest Gas Analyzer data: {e}")
        # Return empty data instead of raising exception to avoid frontend errors
        return []

@router.get("/gas-analyzer/tables")
async def get_gas_analyzer_tables():
    """Get available Gas Analyzer tables information"""
    try:
        # Get all Gas Analyzer tables from database
        all_tables = await db_manager.execute_query("SHOW TABLES")
        
        # Filter gas device tables with date format: gas_device_X_session_YYYYMMDD_HHMMSS
        gas_tables = []
        for table_row in all_tables:
            table_name = list(table_row.values())[0]
            if table_name.startswith('gas_device_') and 'session_' in table_name and len(table_name) > 30:
                # Get table info
                count_query = f"SELECT COUNT(*) as count FROM {table_name}"
                count_result = await db_manager.execute_query(count_query)
                record_count = count_result[0]['count'] if count_result else 0
                
                # Extract device number and date/time from table name
                parts = table_name.split('_')
                device_number = parts[2] if len(parts) > 2 else "unknown"
                
                # Find session date/time part
                session_part = ""
                for i, part in enumerate(parts):
                    if part == "session" and i + 1 < len(parts):
                        session_part = "_".join(parts[i+1:])
                        break
                
                gas_tables.append({
                    "table_name": table_name,
                    "record_count": record_count,
                    "device_number": device_number,
                    "session_datetime": session_part
                })
        
        # Sort by table name (which includes date/time) - latest first
        gas_tables.sort(key=lambda x: x['table_name'], reverse=True)
        
        return {
            "total_tables": len(gas_tables),
            "tables": gas_tables,
            "latest_table": gas_tables[0]['table_name'] if gas_tables else None
        }
        
    except Exception as e:
        logger.error(f"Failed to get Gas Analyzer tables info: {e}")
        return {
            "total_tables": 0,
            "tables": [],
            "latest_table": None,
            "error": str(e)
        }

@router.get("/gas-analyzer/history")
async def get_gas_analyzer_history():
    """Get gas analyzer history"""
    return []

@router.get("/flow-meter/latest")
async def get_flow_meter_latest(limit: int = 2):
    """Get latest flow meter data from the most recent flow device table"""
    try:
        # Get all Flow Meter tables from database
        all_tables = await db_manager.execute_query("SHOW TABLES")
        
        # Filter flow device 1 tables with date format: flow_device_1_session_YYYYMMDD_HHMMSS
        flow_tables = []
        for table_row in all_tables:
            table_name = list(table_row.values())[0]
            if table_name.startswith('flow_device_1_session_') and len(table_name) > 30:
                flow_tables.append(table_name)
        
        if not flow_tables:
            logger.warning("No Flow Meter tables found")
            return []
        
        # Sort flow tables by name to find the most recent one
        flow_tables.sort(reverse=True)  # Latest first
        latest_table = flow_tables[0]
        
        logger.info(f"Using latest Flow Meter table: {latest_table}")
        
        # Get the latest 2 records from the most recent flow meter table (Channel A and B)
        query = f"""
        SELECT id, device_number, timestamp, created_at, serial_number, value
        FROM {latest_table} 
        ORDER BY id DESC 
        LIMIT {limit}
        """
        
        data = await db_manager.execute_query(query)
        
        if data:
            logger.info(f"Flow Meter data found from {latest_table}: {len(data)} records")
            
            # Process the flow meter data
            processed_data = []
            
            for row in data:
                # Parse the value field to extract flow data
                value_str = row.get('value', '')
                logger.info(f"Raw value string: {value_str}")
                
                # Determine channel type
                channel = 'Unknown'
                volumetric_flow = None
                temperature = None
                
                # Check if value contains A: or B: anywhere in the string
                if 'A:' in value_str:
                    channel = 'A'
                    # Parse Channel A: '20.08.2025 10:43:07;A:; 32,65; +14200,747; -49,486;'
                    parts = value_str.split(';')
                    logger.info(f"Channel A parts: {parts}")
                    
                    # Find the A: part and get the next value (volumetric flow)
                    for i, part in enumerate(parts):
                        if 'A:' in part and i + 1 < len(parts):
                            try:
                                # Next part after 'A:' is volumetric flow
                                flow_str = parts[i + 1].strip().replace(',', '.')
                                volumetric_flow = float(flow_str)
                                logger.info(f"Parsed Channel A flow: {volumetric_flow}")
                                
                                # For Channel A, temperature might be in a later part
                                # For now, let's assume no temperature data in this format
                                temperature = None
                                break
                            except (ValueError, IndexError) as e:
                                logger.warning(f"Failed to parse Channel A data: {e}")
                            
                elif 'B:' in value_str:
                    channel = 'B'
                    # Parse Channel B: '20.08.2025 10:43:07;B:; 0,00; +10704,037; -0,729;'
                    parts = value_str.split(';')
                    logger.info(f"Channel B parts: {parts}")
                    
                    # Find the B: part and get the next value (volumetric flow)
                    for i, part in enumerate(parts):
                        if 'B:' in part and i + 1 < len(parts):
                            try:
                                # Next part after 'B:' is volumetric flow
                                flow_str = parts[i + 1].strip().replace(',', '.')
                                volumetric_flow = float(flow_str)
                                logger.info(f"Parsed Channel B flow: {volumetric_flow}")
                                # Channel B doesn't have temperature
                                break
                            except (ValueError, IndexError) as e:
                                logger.warning(f"Failed to parse Channel B data: {e}")
                
                # Process timestamp format: '20.08.2025 07:12:35' -> ISO format
                timestamp_raw = row.get('timestamp', '')
                processed_timestamp = None
                if timestamp_raw:
                    try:
                        # Convert from '20.08.2025 07:12:35' to '2025-08-20T07:12:35'
                        from datetime import datetime
                        dt = datetime.strptime(str(timestamp_raw), '%d.%m.%Y %H:%M:%S')
                        processed_timestamp = dt.isoformat()
                    except (ValueError, TypeError) as e:
                        logger.warning(f"Failed to parse timestamp: {timestamp_raw}, error: {e}")
                        processed_timestamp = str(timestamp_raw)
                
                processed_row = {
                    'id': row.get('id'),
                    'device_number': row.get('device_number'),
                    'timestamp': processed_timestamp,
                    'created_at': row.get('created_at'),
                    'serial_number': row.get('serial_number'),
                    'channel': channel,
                    'volumetric_flow': volumetric_flow,
                    'temperature': temperature,
                    'raw_value': value_str,
                    'source_table': latest_table
                }
                
                processed_data.append(processed_row)
                
                logger.info(f"Processed flow data - Channel {channel}: Flow={volumetric_flow} l/h, Temp={temperature}°C")
        
        # Convert datetime objects to ISO strings
        for row in processed_data:
            for key, value in row.items():
                if isinstance(value, datetime):
                    # Don't add 'Z' suffix since these are local timestamps, not UTC
                    row[key] = value.isoformat()
        
        return processed_data
        
    except Exception as e:
        logger.error(f"Failed to get latest Flow Meter data: {e}")
        # Return empty data instead of raising exception to avoid frontend errors
        return []

@router.get("/flow-meter/history")
async def get_flow_meter_history():
    """Get flow meter history"""
    return []

@router.get("/shaft-power/latest")
async def get_shaft_power_latest():
    """Get latest shaft power data"""
    return []

@router.get("/shaft-power/history")
async def get_shaft_power_history():
    """Get shaft power history"""
    return []

@router.get("/rh-meter/latest")
async def get_rh_meter_latest():
    """Get latest RH meter data"""
    return []

@router.get("/rh-meter/history")
async def get_rh_meter_history():
    """Get RH meter history"""
    return []

@router.get("/esp32/latest")
async def get_esp32_latest():
    """Get latest ESP32 data"""
    return []

@router.get("/esp32/history")
async def get_esp32_history():
    """Get ESP32 history"""
    return []

# MySQL Connection Status endpoint
@router.get("/mysql/status")
async def get_mysql_status():
    """Get MySQL connection status without exposing sensitive information"""
    try:
        # Load MySQL configuration
        mysql_config = db_manager._load_mysql_config()
        
        # Test database connection
        connection_status = "disconnected"
        status_message = "Not connected"
        
        try:
            # Test connection using the database manager
            with db_manager.sync_engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                result.fetchone()
                connection_status = "connected"
                status_message = "Connection successful"
        except Exception as conn_error:
            connection_status = "error"
            status_message = f"Connection failed: {str(conn_error)}"
        
        # Return only safe information
        return {
            "status": connection_status,
            "host": mysql_config.get('host', 'unknown').replace('.rds.amazonaws.com', '.rds.aws'),  # Mask full hostname
            "database": mysql_config.get('database', 'unknown'),
            "port": mysql_config.get('port', 3306),
            "last_check": datetime.now().isoformat(),
            "message": status_message
        }
    except Exception as e:
        return {
            "status": "error",
            "host": "unknown",
            "database": "unknown", 
            "port": 3306,
            "last_check": datetime.now().isoformat(),
            "message": f"Configuration error: {str(e)}"
        }

@router.post("/mysql/test-connection")
async def test_mysql_connection():
    """Test MySQL connection without exposing credentials"""
    try:
        # Actually test the database connection
        test_success = False
        status = "disconnected"
        message = "Connection test failed"
        
        try:
            # Test connection using the database manager
            with db_manager.sync_engine.connect() as conn:
                result = conn.execute(text("SELECT 1 as test"))
                test_result = result.fetchone()
                if test_result and test_result[0] == 1:
                    test_success = True
                    status = "connected" 
                    message = "Connection test successful"
        except Exception as conn_error:
            status = "error"
            message = f"Connection test failed: {str(conn_error)}"
        
        return {
            "success": test_success,
            "status": status,
            "message": message,
            "test_time": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "success": False,
            "status": "error",
            "message": f"Test failed: {str(e)}",
            "test_time": datetime.now().isoformat()
        }